import { type EpPropMergeTypeWithNull } from "element-plus";

export interface QingpaiFileReportResponse {
  Stage: "Success" | "Fail";
  TotalCount: number;
  SuccessCount: number;
  FailCount: number;
  ErrorDetail: Record<string, string>;
}
export interface CheckedReportInputDTO {
  StartTime: string;
  EndTime: string;
  SchoolName: EpPropMergeTypeWithNull<string>;
  SchoolCategory: EpPropMergeTypeWithNull<string>;
  Grade: EpPropMergeTypeWithNull<string>;
}
export interface CheckedReportItem {
  ReportCount?: number;
  CheckedCount?: number;
  UnCheckedCount?: number;
  CheckedRate?: number;
  OrderIdx?: string;
  SchoolName?: string;
  SchoolCategory?: string;
  Grade?: string;
  Class?: string;
}
