import { type RouteVO } from "@/api/system/menu";

const routes: RouteVO[] = [
  {
    path: "/spine",
    component: "Layout",
    name: "SpinalScreening",
    sort: 120,
    redirect: "/spine/memberManagement",
    meta: {
      title: "脊柱筛查项目",
      hidden: false,
      roles: ["superAdmin", "assistant"],
    },
    children: [
      {
        path: "missionManagement",
        name: "SpinalScreeningMissionManagement",
        component: "spine/missionManagement/index",
        meta: {
          title: "宣教推荐设置",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "memberManagement",
        name: "SpinalScreeningMemberManagement",
        component: "spine/memberManagement/index",
        meta: {
          title: "成员管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default routes;
