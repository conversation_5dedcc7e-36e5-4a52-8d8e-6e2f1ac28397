<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <div />
          </template>
          <template #right>
            <el-button type="primary" @click="handleAddDirectory">添加目录</el-button>
            <el-button type="warning" @click="resetToOriginalData">重置数据</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          row-key="path"
          :data="pageData"
          :tree-props="{
            children: 'children',
            hasChildren: 'hasChildren',
          }"
          class="data-table__content"
          border
          @row-click="handleRowClick"
        >
          <el-table-column label="菜单名称" min-width="200">
            <template #default="scope">
              <template v-if="scope.row.meta?.icon && scope.row.meta.icon.startsWith('el-icon')">
                <el-icon style="vertical-align: -0.15em">
                  <component :is="scope.row.meta.icon.replace('el-icon-', '')" />
                </el-icon>
              </template>
              <template v-else-if="scope.row.meta?.icon">
                <div :class="`i-svg:${scope.row.meta.icon}`" />
              </template>
              {{ scope.row.meta?.title || scope.row.name }}
            </template>
          </el-table-column>

          <el-table-column label="类型" align="center" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.component === 'Layout'" type="warning">目录</el-tag>
              <el-tag v-else-if="scope.row.children && scope.row.children.length > 0" type="info">
                菜单
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="路由名称" align="left" width="150" prop="name" />
          <el-table-column label="路由路径" align="left" width="150" prop="path" />
          <el-table-column label="组件路径" align="left" width="250" prop="component" />
          <el-table-column label="重定向" align="center" width="200" prop="redirect" />
          <el-table-column label="状态" align="center" width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.meta?.hidden === true" type="info">隐藏</el-tag>
              <el-tag v-else type="success">显示</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" width="80" prop="sort" />
          <el-table-column label="角色权限" align="center" width="200">
            <template #default="scope">
              <el-tag
                v-for="role in scope.row.meta?.roles?.slice(0, 2)"
                :key="role"
                size="small"
                style="margin-right: 4px"
              >
                {{ role }}
              </el-tag>
              <el-tag v-if="scope.row.meta?.roles && scope.row.meta.roles.length > 2" size="small">
                +{{ scope.row.meta.roles.length - 2 }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="220">
            <template #default="scope">
              <el-button type="primary" link size="small" icon="plus" @click="handleAdd(scope.row)">
                新增
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                icon="edit"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                link
                size="small"
                icon="delete"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </BaseTableSearchContainer>

    <!-- 菜单编辑抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose"
    >
      <MenuForm
        :operation-type="operationType"
        :current-data="currentEditRow"
        :parent-menu-options="parentMenuOptions"
        @submit="handleSaveMenu"
        @cancel="handleDrawerClose"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import { asyncRoutes } from "@/menu/index";
import type { RouteVO } from "@/api/system/menu";
import { ElMessage, ElMessageBox } from "element-plus";
import MenuForm from "./components/MenuForm.vue";

const handleRowClick = (row: any) => {};

defineOptions({
  name: "debugPage",
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

// 弹窗相关状态
const drawerVisible = ref<boolean>(false);
const drawerTitle = ref<string>("添加目录");
const currentEditRow = ref<RouteVO | null>(null);
const operationType = ref<"add" | "edit" | "addChild">("add");

const { tableLoading, pageData, tableRef, tableResize, dialogConfirmLoading } =
  useTableConfig<unknown>();

// 生成唯一ID
const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substring(2, 11);
};

// 构建父级菜单选项
const parentMenuOptions = computed(() => {
  const buildOptions = (routes: RouteVO[], level = 0): any[] => {
    return routes.map((route) => ({
      title: route.meta?.title || route.name,
      path: route.path,
      children: route.children ? buildOptions(route.children, level + 1) : undefined,
      disabled: level >= 2, // 限制最多两级
    }));
  };

  return buildOptions(pageData.value as RouteVO[]);
});

// 为路由数据添加父子关系
const processRouteData = (routes: RouteVO[], parentPath = ""): RouteVO[] => {
  return routes.map((route) => {
    const processedRoute = {
      ...route,
      id: route.path || generateId(),
      parentId: parentPath || null,
    };

    if (route.children && route.children.length > 0) {
      processedRoute.children = processRouteData(route.children, processedRoute.id);
    }

    return processedRoute;
  });
};

// 添加目录
const handleAddDirectory = () => {
  operationType.value = "add";
  drawerTitle.value = "添加目录";
  currentEditRow.value = null;
  drawerVisible.value = true;
};

// 新增子菜单
const handleAdd = (row: RouteVO) => {
  operationType.value = "addChild";
  drawerTitle.value = "新增菜单";
  currentEditRow.value = row;
  drawerVisible.value = true;
};

// 编辑菜单
const handleEdit = (row: RouteVO) => {
  operationType.value = "edit";
  drawerTitle.value = "编辑菜单";
  currentEditRow.value = { ...row };
  drawerVisible.value = true;
};

// 删除菜单
const handleDelete = async (row: RouteVO) => {
  try {
    // 检查是否有子菜单
    const hasChildren = row.children && row.children.length > 0;
    const confirmMessage = hasChildren
      ? `确定要删除菜单"${row.meta?.title || row.name}"吗？\n删除后其所有子菜单也将被删除，且无法恢复！`
      : `确定要删除菜单"${row.meta?.title || row.name}"吗？删除后将无法恢复！`;

    await ElMessageBox.confirm(confirmMessage, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: false,
    });

    // 执行删除逻辑（级联删除）
    deleteRouteFromData(row);

    // 数据持久化
    saveDataToStorage();

    ElMessage.success("删除成功");
  } catch {
    // 用户取消删除
  }
};

// 从数据中删除路由
const deleteRouteFromData = (targetRoute: RouteVO) => {
  const deleteFromArray = (routes: RouteVO[]): RouteVO[] => {
    return routes.filter((route) => {
      if (route.path === targetRoute.path) {
        return false;
      }
      if (route.children) {
        route.children = deleteFromArray(route.children);
      }
      return true;
    });
  };

  pageData.value = deleteFromArray(pageData.value as RouteVO[]);
};

// 关闭抽屉
const handleDrawerClose = () => {
  drawerVisible.value = false;
  currentEditRow.value = null;
};

// 保存菜单数据
const handleSaveMenu = (menuData: RouteVO) => {
  if (operationType.value === "add") {
    // 添加新的顶级目录
    (pageData.value as RouteVO[]).push(menuData);
  } else if (operationType.value === "addChild") {
    // 添加子菜单
    addChildToRoute(menuData, currentEditRow.value!);
  } else if (operationType.value === "edit") {
    // 编辑现有菜单
    updateRouteInData(menuData);
  }

  // 重新排序
  sortRoutes(pageData.value as RouteVO[]);

  // 数据持久化
  saveDataToStorage();

  handleDrawerClose();
  ElMessage.success("保存成功");
};

// 添加子菜单到指定路由
const addChildToRoute = (childRoute: RouteVO, parentRoute: RouteVO) => {
  const addToArray = (routes: RouteVO[]) => {
    routes.forEach((route) => {
      if (route.path === parentRoute.path) {
        if (!route.children) {
          route.children = [];
        }
        route.children.push(childRoute);
      } else if (route.children) {
        addToArray(route.children);
      }
    });
  };

  addToArray(pageData.value as RouteVO[]);
};

// 更新路由数据（支持跨目录编辑）
const updateRouteInData = (updatedRoute: RouteVO) => {
  const originalPath = currentEditRow.value?.path;
  if (!originalPath) return;

  // 先从原位置删除
  const deleteFromArray = (routes: RouteVO[]): RouteVO[] => {
    return routes.filter((route) => {
      if (route.path === originalPath) {
        return false;
      }
      if (route.children) {
        route.children = deleteFromArray(route.children);
      }
      return true;
    });
  };

  pageData.value = deleteFromArray(pageData.value as RouteVO[]);

  // 然后添加到新位置
  const parentId = (updatedRoute as any).parentId;
  if (parentId) {
    // 添加到指定父级菜单下
    const addToParent = (routes: RouteVO[]) => {
      routes.forEach((route) => {
        if (route.path === parentId) {
          if (!route.children) {
            route.children = [];
          }
          route.children.push(updatedRoute);
        } else if (route.children) {
          addToParent(route.children);
        }
      });
    };
    addToParent(pageData.value as RouteVO[]);
  } else {
    // 添加到顶级
    (pageData.value as RouteVO[]).push(updatedRoute);
  }
};

// 路由排序
const sortRoutes = (routes: RouteVO[]) => {
  routes.sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));
  routes.forEach((route) => {
    if (route.children) {
      sortRoutes(route.children);
    }
  });
};

// 数据持久化相关
const STORAGE_KEY = "debug_menu_data";

// 保存数据到localStorage
const saveDataToStorage = () => {
  try {
    const dataToSave = JSON.stringify(pageData.value);
    localStorage.setItem(STORAGE_KEY, dataToSave);
    console.log("菜单数据已保存到localStorage");
  } catch (error) {
    console.error("保存菜单数据失败:", error);
  }
};

// 从localStorage加载数据
const loadDataFromStorage = (): RouteVO[] | null => {
  try {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      return JSON.parse(savedData);
    }
  } catch (error) {
    console.error("加载菜单数据失败:", error);
  }
  return null;
};

// 清除存储的数据
const clearStorageData = () => {
  localStorage.removeItem(STORAGE_KEY);
  ElMessage.success("已清除存储的菜单数据");
  handleGetTableList();
};

// 重置为原始数据
const resetToOriginalData = () => {
  clearStorageData();
};

const handleGetTableList = async () => {
  // 优先从localStorage加载数据
  const savedData = loadDataFromStorage();
  if (savedData) {
    pageData.value = savedData;
    console.log("从localStorage加载菜单数据");
  } else {
    // 如果没有保存的数据，使用原始数据
    const processedData = processRouteData(asyncRoutes);
    pageData.value = processedData;
    console.log("使用原始菜单数据");
  }
};

onMounted(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>

<style lang="scss" scoped></style>
