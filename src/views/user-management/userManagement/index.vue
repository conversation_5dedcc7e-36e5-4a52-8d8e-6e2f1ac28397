<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="角色">
                <el-select
                  v-model="queryParams.RoleType"
                  placeholder="角色"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  @change="handleChangeRoleType"
                >
                  <el-option
                    v-for="item in roleList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.RoleType!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="handleShowOrgOrDept()" label="机构">
                <HospitalSelect v-model="queryParams.OrgIds" multiple />
              </el-form-item>
              <el-form-item v-if="handleShowOrgOrDept()" label="科室">
                <el-select
                  v-model="queryParams.DeptIds"
                  filterable
                  clearable
                  multiple
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  :disabled="!queryParams.OrgIds || queryParams.OrgIds.length > 1"
                >
                  <el-option
                    v-for="(item, index) in deptList"
                    :key="'dept' + index"
                    :label="item.Name"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="queryParams.RoleType === 'assistant'" label="是否上传微信二维码">
                <el-select
                  v-model="queryParams.HasWeChatQrCode"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 80px"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="queryParams.RoleType === 'doctor'" label="是否上传电子执业证书">
                <el-select
                  v-model="queryParams.HasElectronicPractice"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 80px"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="
                  queryParams.RoleType === 'doctor' ||
                  queryParams.RoleType === 'therapist' ||
                  queryParams.RoleType === 'nurse'
                "
                label="是否上传证件照片"
              >
                <el-select
                  v-model="queryParams.HasDoctorQualifyImg"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 80px"
                >
                  <el-option label="是" :value="1" />
                  <el-option label="否" :value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/用户名/昵称/手机号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handlePreviewOrEdit(null, false)">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="flex: 1; text-align: center"
        >
          <el-table-column fixed="right" label="操作" width="200" align="center">
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="handlePreviewOrEdit(scope.row, true)"
              >
                查看
              </el-button>
              <el-button
                link
                type="primary"
                size="small"
                @click="handlePreviewOrEdit(scope.row, false)"
              >
                编辑
              </el-button>
              <el-button link type="primary" size="small" @click="handleSetUserRoles(scope.row)">
                角色
              </el-button>
              <el-button
                v-if="handleShowQrCode(scope.row)"
                link
                type="primary"
                size="small"
                @click="handleClickQrCode(scope.row)"
              >
                查看二维码
              </el-button>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="角色" align="center">
            <template #default="scope">
              {{ onGetRoleNames(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column prop="UserName" label="用户名" align="center" />
          <el-table-column prop="NickName" label="昵称" align="center" />
          <el-table-column prop="Name" label="姓名" align="center" />
          <el-table-column prop="Sex" label="性别" align="center" />
          <el-table-column prop="PhoneNumber" label="手机号" width="120" align="center" />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="140"
            :formatter="tableDateFormat"
            align="center"
          />
          <el-table-column label="是否启用" width="120" align="center">
            <template #default="scope">
              {{ scope.row.IsEnabled ? "是" : "否" }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex!"
          v-model:limit="queryParams.PageSize!"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="dialogVisible.qrCode"
      :title="userQrCodeInfo.Name"
      width="600px"
      align-center
      destroy-on-close
    >
      <div class="qr-code-container">
        <!-- 左侧：微信扫码报到 -->
        <div class="qr-code-item">
          <div class="qr-code-title">微信扫码报到</div>
          <div class="qr-code-wrapper">
            <img :src="userQrCodeInfo.QrCode" alt="微信扫码报到" class="qr-code-image" />
          </div>
        </div>

        <!-- 右侧：微信扫码预问诊 -->
        <div class="qr-code-item">
          <div class="qr-code-title">微信扫码预问诊</div>
          <div class="qr-code-wrapper">
            <QRCode :value="userQrCodeInfo.PreConsultationQRUrl" :size="200" :need-upload="false" />
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-model="dialogVisible.setUserRoles" title="设置角色" width="600px" destroy-on-close>
      <SetUserRolesContent ref="setUserRolesRef" :role-ids="userRoleIds" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.setUserRoles = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleSetUserRolesConfirm"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="dialogVisible.userContent"
      :title="contentTitle"
      width="900px"
      destroy-on-close
    >
      <UserContent ref="userContentRef" :user-id="clickUserId" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.userContent = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleUserContentConfirm"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Passport_Api from "@/api/passport";
import { UserProfileParams } from "@/api/passport/types";
import { getDeptList, getDictionaryList } from "@/utils/dict";
import { EpPropMergeTypeWithNull } from "element-plus";
import QRCode from "@/components/QRCode/index.vue";
import UserContent from "./components/UserContent.vue";
import SetUserRolesContent from "./components/SetUserRolesContent.vue";

defineOptions({
  name: "UserManagement",
});

interface PageUserQrCodeInfo {
  QrCode: string;
  PreConsultationQRUrl: string;
  Name: string;
}
interface PageDialogVisible {
  qrCode: boolean;
  setUserRoles: boolean;
  userContent: boolean;
}

const queryParams = ref<UserProfileParams>({
  Keyword: "",
  DtoTypeName: "OrganizationDoctorListOutputDto",
  Pageable: true,
  RoleType: null,
  HasWeChatQrCode: null,
  HasElectronicPractice: null,
  OrgIds: null,
  DeptIds: null,
  PageSize: 20,
  PageIndex: 1,
  HasTherapistQualifyImg: null,
  HasDoctorQualifyImg: null,
  HasNurseQualifyImg: null,
  IsEnabled: null,
});
const roleList = ref<BaseRole[]>([]);
const deptList = ref<BaseDepartment[]>([]);
const isUploadIdPhotos = ref<EpPropMergeTypeWithNull<boolean>>(null);
const dialogConfirmLoading = ref<boolean>(false);
const setUserRolesRef = useTemplateRef("setUserRolesRef");
const userContentRef = useTemplateRef("userContentRef");
const userRoleIds = ref<string[]>([]);
const isPreview = ref<boolean>(false);
const clickUserId = ref<string>("");
const contentTitle = ref<string>("添加");
const sexDictList = ref<ReadDict[]>([]);
const workerTitleDictList = ref<ReadDict[]>([]);
const workerTypeDictList = ref<ReadDict[]>([]);
provide("isPreview", isPreview);
provide("roleList", roleList);
provide("sexDictList", sexDictList);
provide("workerTitleDictList", workerTitleDictList);
provide("workerTypeDictList", workerTypeDictList);
const userQrCodeInfo = ref<PageUserQrCodeInfo>({
  QrCode: "",
  PreConsultationQRUrl: "",
  Name: "",
});
const dialogVisible = ref<PageDialogVisible>({
  qrCode: false,
  setUserRoles: false,
  userContent: false,
});

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<BaseUserProfile>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: BaseUserProfile | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
  clickUserId.value = row?.Id || "";
  let orgId = "";
  // 获取所有的dict数据
  if (row && row.Organization) {
    orgId = row.Organization.Id!;
  }
  onGetAllDict(orgId);
  dialogVisible.value.userContent = true;
};

const onGetRoleNames = (row: BaseUserProfile) => {
  if (!row) return "";
  return (row.Roles as BaseRole[])?.map((item) => item.Name).join("，");
};

const handleShowQrCode = (row: BaseUserProfile) => {
  if (!row || !row.Roles) return false;
  return (row.Roles as BaseRole[]).some(
    (item) =>
      item.RoleType === "doctor" || item.RoleType === "therapist" || item.RoleType === "nurse"
  );
};

const handleClickQrCode = (row: BaseUserProfile) => {
  if (!row.UserWork || !row.UserWork.QrCode) {
    ElMessage({
      type: "error",
      message: "还没有二维码,请编辑保存才会生成二维码",
    });
    return;
  }
  userQrCodeInfo.value.QrCode = row.UserWork.QrCode;
  userQrCodeInfo.value.PreConsultationQRUrl = `https://oss-biz.kangfx.com?docId=${row.Id}&scene=9`;
  userQrCodeInfo.value.Name = row.Name;
  dialogVisible.value.qrCode = true;
};

const handleSetUserRoles = (row: BaseUserProfile) => {
  clickUserId.value = row.Id;
  userRoleIds.value = (row.Roles as BaseRole[]).map((item) => item.Id!);
  dialogVisible.value.setUserRoles = true;
};

const handleShowOrgOrDept = () => {
  return (
    queryParams.value.RoleType === "doctor" ||
    queryParams.value.RoleType === "therapist" ||
    queryParams.value.RoleType === "nurse"
  );
};

const handleChangeRoleType = () => {
  queryParams.value.HasElectronicPractice = null;
  queryParams.value.HasWeChatQrCode = null;
  queryParams.value.OrgIds = null;
  queryParams.value.DeptIds = null;
  queryParams.value.HasDoctorQualifyImg = null;
  queryParams.value.HasTherapistQualifyImg = null;
  queryParams.value.HasNurseQualifyImg = null;
  isUploadIdPhotos.value = null;
};
const handleGetTableList = async () => {
  const copyData: UserProfileParams = JSON.parse(JSON.stringify(queryParams.value));
  tableLoading.value = true;
  const res = await Passport_Api.getUserProfile(copyData);
  if (res.Type === 200) {
    pageData.value = res.Data.Row;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const onGetPlantFormRoleList = async () => {
  const res = await Passport_Api.read({});
  if (res.Type === 200) {
    roleList.value = res.Data;
  }
};

const onGetDeptList = async (newVal: null | string[]) => {
  if (!newVal || newVal.length > 1) {
    deptList.value = [];
    return;
  }
  const list = await getDeptList({
    OrgId: newVal[0],
  });
  deptList.value = list;
};

const handleSetUserRolesConfirm = async () => {
  const roleIds = setUserRolesRef.value?.handleSubmit() || [];
  dialogConfirmLoading.value = true;
  Passport_Api.setRoles({
    UserId: clickUserId.value,
    RoleIds: roleIds,
  })
    .then((res) => {
      if (res.Type === 200) {
        ElMessage({
          type: "success",
          message: "设置角色成功",
        });
        dialogVisible.value.setUserRoles = false;
        handleGetTableList();
      }
    })
    .catch(() => {
      ElMessage({
        type: "error",
        message: "设置角色失败",
      });
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleUserContentConfirm = async () => {
  dialogConfirmLoading.value = true;
  userContentRef.value
    ?.handleSubmit()
    .then((isSuccess) => {
      console.log("isSuccess", isSuccess);
      if (isSuccess) {
        ElMessage({
          type: "success",
          message: "操作成功",
        });
        dialogVisible.value.userContent = false;
        handleGetTableList();
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleSetIsUploadIdPhotos = (newVal: EpPropMergeTypeWithNull<boolean>) => {
  if (!newVal) {
    delete queryParams.value.HasTherapistQualifyImg;
    delete queryParams.value.HasNurseQualifyImg;
    delete queryParams.value.HasDoctorQualifyImg;
    return;
  }
  switch (queryParams.value.RoleType) {
    case "doctor":
      queryParams.value.HasDoctorQualifyImg = 1;
      delete queryParams.value.HasTherapistQualifyImg;
      delete queryParams.value.HasNurseQualifyImg;
      break;
    case "therapist":
      queryParams.value.HasTherapistQualifyImg = 1;
      delete queryParams.value.HasDoctorQualifyImg;
      delete queryParams.value.HasNurseQualifyImg;
      break;
    case "nurse":
      queryParams.value.HasNurseQualifyImg = 1;
      delete queryParams.value.HasTherapistQualifyImg;
      delete queryParams.value.HasDoctorQualifyImg;
      break;
    default:
      delete queryParams.value.HasTherapistQualifyImg;
      delete queryParams.value.HasNurseQualifyImg;
      delete queryParams.value.HasDoctorQualifyImg;
      break;
  }
};

const onGetAllDict = async (orgId: string) => {
  const res = await Promise.all([
    getDictionaryList("SexDict", orgId),
    getDictionaryList("WorkerTitleDict", orgId),
    getDictionaryList("WorkerTypeDict", orgId),
  ]);
  sexDictList.value = res[0];
  workerTitleDictList.value = res[1];
  workerTypeDictList.value = res[2];
};

/** watch监听queryParams.OrgIds */
watch(
  () => queryParams.value.OrgIds,
  (newVal) => {
    onGetDeptList(newVal);
  }
);
watch(
  () => isUploadIdPhotos.value,
  (newVal) => {
    handleSetIsUploadIdPhotos(newVal);
  }
);

onMounted(() => {
  // 获取平台的角色
  onGetPlantFormRoleList();
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped>
.qr-code-container {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  justify-content: space-around;
  padding: 20px;
}

.qr-code-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
}

.qr-code-title {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.qr-code-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.qr-code-image {
  display: block;
  width: 200px;
  height: 200px;
  object-fit: contain;
}
</style>
