<template>
  <el-tabs v-model="activeName" v-loading="loading" type="card">
    <el-tab-pane label="基本信息" name="user">
      <UserBase ref="userBaseRef" :user-info="userInfo" />
    </el-tab-pane>
    <el-tab-pane v-if="isShowIdentity" label="身份认证" name="identity">
      <IdentityContent
        ref="identityContentRef"
        :user-certificates="userCertificates"
        :user-id="props.userId"
      />
    </el-tab-pane>
    <el-tab-pane v-if="isShowProfession" label="职业认证" name="profession">
      <CertificationContent
        ref="certificationContentRef"
        :professional-certification="professionalCertification"
        :worker-type="workerType"
        :worker-title="workerTitle"
        :user-id="props.userId"
      />
    </el-tab-pane>
    <el-tab-pane v-if="isShowOrganization" label="入住机构" name="organization">
      <OrganizationContent ref="organizationContentRef" :organization-info="organizationInfo" />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { CreateUserInputDTO, OrganizationAuthentication } from "@/api/passport/types";

import UserBase from "./UserBase.vue";
import OrganizationContent from "./OrganizationContent.vue";

const activeName = ref("user");
const userBaseRef = useTemplateRef("userBaseRef");
const identityContentRef = useTemplateRef("identityContentRef");
const certificationContentRef = useTemplateRef("certificationContentRef");
const organizationContentRef = useTemplateRef("organizationContentRef");

const userInfo = ref<CreateUserInputDTO | null>(null);
const userCertificates = ref<UserCertificate[]>([]);
const professionalCertification = ref<UserCertificate[]>([]);
const organizationInfo = ref<OrganizationAuthentication | null>(null);
const workerType = ref<string>("");
const workerTitle = ref<string>("");
const isShowOrganization = ref<boolean>(false);
const isShowProfession = ref<boolean>(false);
const isShowIdentity = ref<boolean>(false);
const loading = ref<boolean>(false);

// 验证所有模块数据是否完整
const validateAllData = (
  userBaseData: CreateUserInputDTO | null | undefined,
  identityData: UserCertificate[] | null | undefined,
  professionalData: any | null | undefined,
  organizationData: OrganizationAuthentication | null | undefined
): boolean => {
  const missingModules: string[] = [];

  // 基本信息始终需要验证
  if (!userBaseData) missingModules.push("基本信息");

  // 只有当对应的显示标志为true时才需要验证数据
  if (isShowIdentity.value && !identityData) missingModules.push("身份认证");
  if (isShowProfession.value && !professionalData) missingModules.push("职业认证");
  if (isShowOrganization.value && !organizationData) missingModules.push("入住机构");

  if (missingModules.length > 0) {
    ElMessage.error(`请完善以下模块的信息：${missingModules.join("、")}`);
    return false;
  }

  return true;
};

// 构建请求列表
const buildRequests = (
  userBaseData: CreateUserInputDTO,
  identityData: UserCertificate[],
  professionalData: any,
  organizationData: OrganizationAuthentication
): Promise<any>[] => {
  const requestList: Promise<any>[] = [];

  // 基本信息请求
  const userApiMethod = userBaseData.Id ? Passport_Api.updatePutPart : Passport_Api.createUser;
  const copyData = JSON.parse(JSON.stringify(userBaseData));
  if (copyData.Id) {
    copyData.UserId = copyData.Id;
  }
  delete copyData.Id;

  const userRequestData = [copyData];
  requestList.push(userApiMethod(userRequestData));

  if (isShowIdentity.value && identityData.length) {
    // 身份认证请求
    requestList.push(Passport_Api.upsertUserCertificatesIdCard(identityData));
  }
  if (isShowProfession.value) {
    // 职业认证请求
    requestList.push(Passport_Api.upsertAuthenticationData([professionalData]));
  }
  if (isShowOrganization.value) {
    // 入住机构请求
    requestList.push(Passport_Api.updateUserOrganization([organizationData]));
  }
  return requestList;
};

// 执行请求并处理结果
const executeRequests = async (requestList: Promise<any>[]): Promise<boolean> => {
  try {
    const results = await Promise.all(requestList);
    // 如果请求结果有 !== 200的 需要将每一个错误的提示信息提示用户
    const errorResults = results.filter((item) => item.Type !== 200);
    if (errorResults.length > 0) {
      errorResults.forEach((item) => {
        ElMessage.error(item.Content || "操作失败");
      });
      return false;
    }
    return results.every((item) => item.Type === 200);
  } catch (error) {
    console.error("请求执行失败:", error);
    return false;
  }
};

const handleSubmit = async (): Promise<boolean> => {
  // 获取所有模块数据
  const userBaseData = await userBaseRef.value?.handleSubmit();
  const identityData = await identityContentRef.value?.handleSubmit();
  const professionalData = await certificationContentRef.value?.handleSubmit();
  const organizationData = organizationContentRef.value?.handleSubmit();
  if (userBaseData) {
    userBaseData.PracticeOrganizationName = organizationData?.PracticeOrganizationName;
  }

  // 验证数据完整性
  if (!validateAllData(userBaseData, identityData, professionalData, organizationData)) {
    return false;
  }

  // 构建并执行请求
  const requestList = buildRequests(
    userBaseData!,
    identityData!,
    professionalData!,
    organizationData!
  );
  return await executeRequests(requestList);
};

// 获取用户数据的API请求函数
const fetchUserData = async (userId: string) => {
  const requestList = [
    Passport_Api.getUserProfile({
      PageIndex: 1,
      PageSize: 1,
      Ids: [userId],
      Keyword: "",
      IsEnabled: null,
      SingleOne: false,
      Pageable: true,
      DtoTypeName: "QueryUserFullOutputDto",
    }),
    Passport_Api.getDoctorOrganizationAuthentications({ userId }),
  ];

  const res = await Promise.all(requestList);

  if (res[0].Type !== 200) {
    ElMessage.error(res[0].Content || "获取用户信息失败");
    return null;
  }
  if (res[1].Type !== 200) {
    ElMessage.error(res[1].Content || "获取用户认证信息失败");
    return null;
  }

  return {
    userProfile: res[0].Data as ListRowTotal<BaseUserProfile>,
    organizationData: res[1].Data as OrganizationAuthentication[],
  };
};

// 设置用户权限和显示标志
const setUserPermissions = (
  roles: string[] | { Name: string; Id: string; RoleType: string }[] | undefined
) => {
  if (roles && roles.length) {
    // 处理角色数据，支持字符串数组和对象数组
    const roleNames =
      Array.isArray(roles) && typeof roles[0] === "string"
        ? (roles as string[])
        : (roles as { Name: string; Id: string; RoleType: string }[]).map((role) => role.RoleType);

    const isWhitelist = roleNames.some((s) => s === "doctor" || s === "therapist" || s === "nurse");
    isShowOrganization.value = isWhitelist;
    isShowProfession.value = isWhitelist;
  } else {
    isShowOrganization.value = false;
    isShowProfession.value = false;
  }
};

// 设置用户基本信息
const setUserBasicInfo = (userItem: BaseUserProfile) => {
  if (!userItem.UserExternalIdentify || !userItem.UserExternalIdentify.WeChatQrCode) {
    userItem.UserExternalIdentify = {
      WeChatQrCode: "",
    };
  }

  userInfo.value = {
    Id: userItem.Id,
    Name: userItem.Name,
    UserName: userItem.UserName!,
    NickName: userItem.NickName!,
    Sex: userItem.Sex!,
    Birthday: userItem.Birthday!,
    Code: userItem.Code!,
    PhoneNumber: userItem.PhoneNumber!,
    IsEnabled: userItem.IsEnabled!,
    IsLocked: userItem.IsLocked!,
    HeadImg: userItem.HeadImg!,
    UserExternalIdentify: userItem.UserExternalIdentify!,
    UserWork: userItem.UserWork!,
  };
};

// 设置用户认证信息
const setUserCertifications = (userItem: BaseUserProfile) => {
  if (userItem && userItem.UserCertificates?.length) {
    userCertificates.value = userItem.UserCertificates.filter(
      (item) =>
        item.CertificateType === "idCard" ||
        item.CertificateType === "idCard_front" ||
        item.CertificateType === "idCard_back"
    );
    professionalCertification.value = userItem.UserCertificates;
  } else {
    userCertificates.value = [];
  }
};

// 设置组织信息
const setOrganizationInfo = (organizationData: OrganizationAuthentication[]) => {
  const userAuthentication = organizationData.filter((s) => s.IsDefault)[0];
  organizationInfo.value = userAuthentication;
};

const onGetUserInfoById = async (userId: string) => {
  loading.value = true;
  try {
    // 获取用户数据
    const data = await fetchUserData(userId);
    if (!data) return;

    const { userProfile, organizationData } = data;
    isShowIdentity.value = true;

    // 设置用户基本信息
    const userItem = userProfile.Row[0];

    workerType.value = userItem.WorkerType!;
    workerTitle.value = userItem.WorkerTitle!;
    setUserPermissions(userItem.Roles);
    setUserBasicInfo(userItem);
    setUserCertifications(userItem);
    setOrganizationInfo(organizationData);
  } catch (error) {
  } finally {
    loading.value = false;
  }
};

interface Props {
  userId: string;
}
const props = defineProps<Props>();
watch(
  () => props.userId,
  (newVal) => {
    if (!newVal) {
      return;
    }
    // 获取用户的所有信息
    onGetUserInfoById(newVal);
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
