<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="机构">
                <HospitalSelect v-model="queryParams.OrgId" />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.DeptName"
                  :org-id="queryParams.OrgId"
                  :disabled="!queryParams.OrgId"
                  keyId="Name"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button icon="refresh" @click="handleReset">重置</el-button>
            <el-button type="primary" @click="handleAddDoctor">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="UserId"
          :height="tableFluidHeight"
          highlight-current-row
          style=" flex: 1;text-align: center"
        >
          <el-table-column label="姓名" align="center">
            <template #default="scope">
              {{ scope.row.Name }}
            </template>
          </el-table-column>
          <el-table-column label="手机号" align="center">
            <template #default="scope">
              {{ scope.row.PhoneNumber }}
            </template>
          </el-table-column>
          <el-table-column label="入驻机构" align="center">
            <template #default="scope">
              {{ scope.row.OrganizationName }}
            </template>
          </el-table-column>
          <el-table-column label="科室" align="center">
            <template #default="scope">
              {{ scope.row.DepartName }}
            </template>
          </el-table-column>
          <el-table-column label="职称" align="center">
            <template #default="scope">
              {{ scope.row.WorkTitle }}
            </template>
          </el-table-column>
          <el-table-column label="执业证书编号" align="center">
            <template #default="scope">
              {{ scope.row.CertificateNum }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="200" align="center">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleDelete(scope.row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- total > 0 -->
      <template #pagination>
        <Pagination
          v-if="false"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="showDialog" title="添加医生" width="1100">
      <AddDoctorContent ref="addDoctorContentRef" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Consult_Api from "@/api/consult";
import { AllowUploadDoctorItem } from "@/api/consult/types";
import AddDoctorContent from "./components/AddDoctorContent.vue";

defineOptions({
  name: "UploadSupervisingDoctor",
});

const queryParams = ref<any>({
  DeptName: null,
  OrgId: null,
  Keyword: "",
});
const showDialog = ref<boolean>(false);
const addDoctorContentRef = useTemplateRef("addDoctorContentRef");

const allPageData = ref<AllowUploadDoctorItem[]>([]);

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  tableFluidHeight,
  tableResize,
  dialogConfirmLoading,
} = useTableConfig<AllowUploadDoctorItem>();
const handleAddDoctor = () => {
  showDialog.value = true;
};
const handleQuery = () => {
  const { DeptName, OrgId, Keyword } = queryParams.value;

  // 从原始数据中进行筛选
  let filteredData = [...allPageData.value];

  // 按科室名称筛选
  if (DeptName) {
    filteredData = filteredData.filter((item) => item.DepartName === DeptName);
  }

  // 按机构ID筛选
  if (OrgId) {
    filteredData = filteredData.filter((item) => item.Organization === OrgId);
  }

  // 按关键字筛选（姓名或手机号）
  if (Keyword && Keyword.trim()) {
    const keyword = Keyword.trim().toLowerCase();
    filteredData = filteredData.filter((item) => {
      const name = (item.Name || "").toLowerCase();
      const phone = (item.PhoneNumber || "").toLowerCase();
      return name.includes(keyword) || phone.includes(keyword);
    });
  }

  // 更新表格数据
  pageData.value = filteredData;
  total.value = filteredData.length;
};
const handleReset = () => {
  // 重置查询参数
  queryParams.value = {
    DeptName: null,
    OrgId: null,
    Keyword: "",
  };

  // 重置表格数据为原始数据
  pageData.value = allPageData.value;
  total.value = allPageData.value.length;
};
const handleDelete = async (row: AllowUploadDoctorItem) => {
  ElMessageBox.confirm("此操作将将该医生移除监管平台, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      const data = {
        UserId: row.UserId!,
        OrganizationId: row.Organization!,
        UploadToSupervision: false,
      };
      Consult_Api.controlUploadData([data]).then((data) => {
        if (data.Type === 200) {
          ElMessage.success(data.Content);
          handleGetTableList();
        }
      });
    })
    .catch(() => {});
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.getAllowUpload();
  if (res.Type === 200) {
    allPageData.value = res.Data;
    pageData.value = res.Data;
    total.value = res.Data.length;
  }
  tableLoading.value = false;
};
const handleSubmit = () => {
  const params = addDoctorContentRef.value?.handleSubmit();
  console.log("params", params);
  if (!params?.length) {
    ElMessage.warning("请选择医生");
    return;
  }
  dialogConfirmLoading.value = true;
  Consult_Api.controlUploadData(params)
    .then((data) => {
      if (data.Type === 200) {
        ElMessage.success(data.Content);
        showDialog.value = false;
        addDoctorContentRef.value?.clearSelection();
        handleGetTableList();
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
