<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="机构">
                <HospitalSelect v-model="queryParams.organizationId" />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.departmentId"
                  :org-id="queryParams.organizationId"
                  :disabled="!queryParams.organizationId"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Doctor.UserId"
          :height="400"
          highlight-current-row
          style=" flex: 1;text-align: center"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" reserve-selection width="55" />
          <el-table-column label="姓名" align="center">
            <template #default="scope">
              {{ scope.row.Doctor.Name }}
            </template>
          </el-table-column>
          <el-table-column label="入驻机构" align="center">
            <template #default="scope">
              {{ scope.row.Doctor.OrganizationName }}
            </template>
          </el-table-column>
          <el-table-column label="入驻科室" align="center">
            <template #default="scope">
              {{ scope.row.Doctor.DepartmentName }}
            </template>
          </el-table-column>
          <el-table-column label="职称" align="center">
            <template #default="scope">
              {{ scope.row.Doctor.WorkerTitle }}
            </template>
          </el-table-column>
          <el-table-column label="手机号码" align="center">
            <template #default="scope">
              {{ scope.row.Doctor.PhoneNumber }}
            </template>
          </el-table-column>
          <el-table-column label="执业证书编号" align="center">
            <template #default="scope">
              {{ scope.row.Doctor.CertificateNum }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import Consult_Api from "@/api/consult";
import { BaseDoctorItem, GetDocsInputDTO } from "@/api/consult/types";
import { useTableConfig } from "@/hooks/useTableConfig";

const queryParams = ref<GetDocsInputDTO>({
  pageIndex: 1,
  pageSize: 20,
  organizationId: null,
  departmentId: null,
  keyword: null,
  roleTypes: ["doctor"],
  pageAble: true,
});
const selectedDoctorList = ref<
  {
    UserId: string;
    OrganizationId: string;
    UploadToSupervision: boolean;
  }[]
>([]);

const { tableLoading, pageData, total, tableRef, tableResize } = useTableConfig<BaseDoctorItem>();

const handleQuery = () => {
  queryParams.value.pageIndex = 1;
  handleGetTableList();
};
const handleTableSelect = (selection: BaseDoctorItem[]) => {
  selectedDoctorList.value = selection.map((x) => {
    return {
      UserId: x.Doctor.UserId,
      OrganizationId: x.Doctor.OrganizationId,
      UploadToSupervision: true,
    };
  });
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.getDocs(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const handleSubmit = (): {
  UserId: string;
  OrganizationId: string;
  UploadToSupervision: boolean;
}[] => {
  return selectedDoctorList.value;
};
const clearSelection = () => {
  selectedDoctorList.value = [];
  tableRef.value?.clearSelection();
};

onBeforeMount(() => {
  handleGetTableList();
});
defineExpose({
  handleSubmit,
  clearSelection,
});
</script>

<style lang="scss" scoped></style>
